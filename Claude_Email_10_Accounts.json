{"name": "<PERSON> 10 Accounts", "nodes": [{"parameters": {}, "id": "7ac38a3b-e015-4408-9e22-c0fed65c7154", "name": "MANUAL TRIGGER: Start Enhanced Workflow", "type": "n8n-nodes-base.manualTrigger", "position": [-4780, 40], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "search_queries_per_product", "name": "search_queries_per_product", "value": 5, "type": "number"}, {"id": "max_emails_per_batch", "name": "max_emails_per_batch", "value": 50, "type": "number"}, {"id": "delay_between_emails", "name": "delay_between_emails", "value": "30-60", "type": "string"}, {"id": "smtp_accounts_count", "name": "smtp_accounts_count", "value": 10, "type": "number"}, {"id": "content_max_chars", "name": "content_max_chars", "value": 8000, "type": "number"}]}, "options": {}}, "id": "ac589d8b-f225-49c1-ab03-1e722cb9c665", "name": "Configuration & Settings", "type": "n8n-nodes-base.set", "position": [-4560, 40], "typeVersion": 3.4}, {"parameters": {"options": {}}, "id": "376076a3-83eb-404d-a073-2f430e826041", "name": "Loop Through Products", "type": "n8n-nodes-base.splitInBatches", "position": [-4340, 40], "typeVersion": 3}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "id"}, "messages": {"values": [{}]}, "options": {"maxTokens": 1000, "temperature": 0.8}}, "id": "9917643a-2566-4159-81cd-878a68fec35f", "name": "Generate Enhanced Search Queries (GPT-4)", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-4260, -140], "typeVersion": 1.4, "credentials": {"openAiApi": {"id": "7Q39A2hDZW6NSPBC", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"jsCode": "const input = $input.all();\nconst results = [];\n\nfor (const item of input) {\n  try {\n    const queriesText = item.json.response || item.json.text || '';\n    let queries = [];\n    \n    // Try to parse as JSON first\n    try {\n      queries = JSON.parse(queriesText);\n    } catch {\n      // If not JSON, extract queries from text\n      const matches = queriesText.match(/\"([^\"]+)\"/g);\n      if (matches) {\n        queries = matches.map(match => match.replace(/\"/g, ''));\n      } else {\n        // Fallback: split by lines and clean\n        queries = queriesText.split('\\n')\n          .map(line => line.replace(/^\\d+\\.\\s*/, '').trim())\n          .filter(line => line.length > 0 && !line.startsWith('Product:'));\n      }\n    }\n    \n    // Add each query as separate item with product context\n    queries.forEach(query => {\n      if (query && query.trim().length > 3) {\n        results.push({\n          query: query.trim(),\n          product_name: item.json.product_name,\n          product_description: item.json.product_description,\n          product_link: item.json.product_link\n        });\n      }\n    });\n  } catch (error) {\n    console.error('Error parsing queries:', error);\n  }\n}\n\nreturn results;"}, "id": "b11f59e4-1c03-49ba-92b7-37b0cde24d63", "name": "Parse Generated Queries", "type": "n8n-nodes-base.code", "position": [-3900, 40], "typeVersion": 2}, {"parameters": {"options": {}}, "id": "f590d084-97fe-4399-a963-5d9776454526", "name": "Loop Through Search Queries", "type": "n8n-nodes-base.splitInBatches", "position": [-3680, 40], "typeVersion": 3}, {"parameters": {"jsCode": "// Implement intelligent rate limiting\nconst batchIndex = $node['Loop Through Search Queries'].context.nodesExecuted || 0;\nconst baseDelay = 2000; // 2 seconds base\nconst randomFactor = Math.random() * 2000; // 0-2 seconds random\nconst backoffFactor = Math.min(batchIndex * 100, 5000); // Progressive backoff\n\nconst totalDelay = baseDelay + randomFactor + backoffFactor;\n\nreturn [{\n  json: {\n    ...($input.first()?.json || {}),\n    delay_ms: Math.round(totalDelay),\n    batch_index: batchIndex\n  }\n}];"}, "id": "54ecd171-4a68-4c0a-92fa-ca94cc7d012c", "name": "Intelligent Rate Limiting", "type": "n8n-nodes-base.code", "position": [-3460, 40], "typeVersion": 2}, {"parameters": {"amount": "={{ $json.delay_ms }}", "unit": "ms"}, "id": "5ccad985-b1de-43e8-953d-d9e2e5db0595", "name": "Dynamic Wait", "type": "n8n-nodes-base.wait", "position": [-3240, 40], "typeVersion": 1.1, "webhookId": "39317449-c704-4886-958f-f62b08e4da9c"}, {"parameters": {"url": "https://maps.googleapis.com/maps/api/place/textsearch/json", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "options": {"timeout": 10000}}, "id": "448bfc5d-50e1-44e3-8625-ff2b660c4953", "name": "Enhanced Google Maps API Search", "type": "n8n-nodes-base.httpRequest", "position": [-3020, 40], "typeVersion": 4.2}, {"parameters": {"jsCode": "const input = $input.first()?.json;\nif (!input?.results) return [];\n\nconst results = [];\nconst businesses = input.results;\n\nfor (const business of businesses) {\n  // Skip if no website or low quality indicators\n  if (!business.website || \n      business.rating < 3.5 || \n      business.user_ratings_total < 10) {\n    continue;\n  }\n  \n  // Extract and clean website URL\n  let website = business.website;\n  if (!website.startsWith('http')) {\n    website = 'https://' + website;\n  }\n  \n  // Extract domain for email guessing\n  const domain = website.replace(/^https?:\\/\\//, '').replace(/\\/.*$/, '').replace(/^www\\./, '');\n  \n  results.push({\n    business_name: business.name,\n    address: business.formatted_address,\n    website: website,\n    domain: domain,\n    rating: business.rating,\n    review_count: business.user_ratings_total,\n    business_types: business.types,\n    business_status: business.business_status,\n    search_query: input.query,\n    product_name: input.product_name,\n    product_description: input.product_description,\n    product_link: input.product_link,\n    quality_score: (business.rating * 0.4) + (Math.min(business.user_ratings_total / 100, 10) * 0.6)\n  });\n}\n\n// Sort by quality score (higher is better)\nresults.sort((a, b) => b.quality_score - a.quality_score);\n\nreturn results;"}, "id": "c901ed35-3d10-48a0-8f05-2f32dd0c083d", "name": "Extract & Enrich Business Data", "type": "n8n-nodes-base.code", "position": [-2800, 40], "typeVersion": 2}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": {"fields": [{"fieldName": "domain"}]}, "options": {}}, "id": "db4b3f3f-2734-4dd2-bbf0-e256c20b1743", "name": "Remove Duplicate Businesses", "type": "n8n-nodes-base.removeDuplicates", "position": [-2580, 40], "typeVersion": 1.1}, {"parameters": {"options": {}}, "id": "017ca630-2fdb-4f17-ad95-ac118f1f12b5", "name": "Loop Through Businesses", "type": "n8n-nodes-base.splitInBatches", "position": [-2360, 40], "typeVersion": 3}, {"parameters": {"jsCode": "const business = $input.first()?.json;\nif (!business?.website) {\n  return [{ json: { ...business, scraping_error: 'No website URL' } }];\n}\n\n// Multiple scraping strategies\nconst scrapingMethods = [\n  {\n    name: 'jina_reader',\n    url: `https://r.jina.ai/${business.website}`,\n    headers: { 'User-Agent': 'Mozilla/5.0 (compatible; Business-Email-Finder/1.0)' }\n  },\n  {\n    name: 'direct_fetch',\n    url: business.website,\n    headers: { \n      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'\n    }\n  }\n];\n\nreturn [{\n  json: {\n    ...business,\n    scraping_methods: scrapingMethods,\n    current_method_index: 0\n  }\n}];"}, "id": "5400c94a-f16a-465d-b627-d9622e7a098c", "name": "Advanced Website Scraping", "type": "n8n-nodes-base.code", "position": [-2140, 40], "typeVersion": 2}, {"parameters": {"url": "={{ $json.scraping_methods[$json.current_method_index].url }}", "options": {"redirect": {}, "timeout": 15000}}, "id": "ad6df384-b7be-429c-b03c-4877d2d4f3df", "name": "Try Scraping Method", "type": "n8n-nodes-base.httpRequest", "position": [-1920, 40], "typeVersion": 4.2, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const input = $input.first()?.json;\nlet content = '';\n\n// Get content from response\nif (input.data) {\n  content = typeof input.data === 'string' ? input.data : JSON.stringify(input.data);\n} else if (input.body) {\n  content = input.body;\n} else {\n  return [{ json: { ...input, emails: [], content_length: 0, extraction_method: 'failed' } }];\n}\n\n// Advanced email extraction with multiple patterns\nconst emailPatterns = [\n  /\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b/g,\n  /\\b[A-Za-z0-9._%+-]+\\s*\\[?at\\]?\\s*[A-Za-z0-9.-]+\\s*\\[?dot\\]?\\s*[A-Z|a-z]{2,}\\b/g,\n  /mailto:\\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,})/gi\n];\n\nlet emails = new Set();\n\n// Extract emails using multiple patterns\nfor (const pattern of emailPatterns) {\n  const matches = content.match(pattern) || [];\n  matches.forEach(email => {\n    // Clean and normalize email\n    email = email.replace(/mailto:/gi, '')\n                .replace(/\\[at\\]/gi, '@')\n                .replace(/\\[dot\\]/gi, '.')\n                .replace(/\\s+/g, '')\n                .toLowerCase();\n    \n    // Validate email format\n    if (/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n      emails.add(email);\n    }\n  });\n}\n\n// Filter out common generic/spam emails\nconst filteredEmails = Array.from(emails).filter(email => {\n  const genericPatterns = [\n    /noreply/i, /no-reply/i, /donotreply/i,\n    /admin@/i, /webmaster@/i, /postmaster@/i,\n    /example\\./i, /test@/i, /demo@/i,\n    /^[a-z]@/i // Single letter emails\n  ];\n  \n  return !genericPatterns.some(pattern => pattern.test(email));\n});\n\n// Extract additional business context\nconst businessContext = {\n  has_contact_page: /contact|about|team|staff/i.test(content),\n  has_services: /service|product|solution/i.test(content),\n  company_size_indicators: content.match(/\\b(\\d+)\\s*(employee|staff|team|people)\\b/gi) || [],\n  social_media: {\n    linkedin: /linkedin\\.com\\/company/i.test(content),\n    facebook: /facebook\\.com/i.test(content),\n    twitter: /twitter\\.com|x\\.com/i.test(content)\n  }\n};\n\nreturn [{\n  json: {\n    ...input,\n    emails: filteredEmails,\n    email_count: filteredEmails.length,\n    content_length: content.length,\n    content_preview: content.substring(0, 500),\n    business_context: businessContext,\n    extraction_method: input.scraping_methods?.[input.current_method_index]?.name || 'unknown'\n  }\n}];"}, "id": "bc2e7631-dc0c-4ace-8eeb-b0b6044bfb87", "name": "Process Scraped Content & Extract Emails", "type": "n8n-nodes-base.code", "position": [-1700, 40], "typeVersion": 2}, {"parameters": {"jsCode": "const input = $input.first()?.json;\nif (!input.emails || input.emails.length === 0) {\n  return [];\n}\n\nconst results = [];\n\nfor (const email of input.emails) {\n  // Basic email validation\n  if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) continue;\n  \n  // Extract name parts from email\n  const localPart = email.split('@')[0];\n  const domain = email.split('@')[1];\n  \n  // Guess name from email\n  let firstName = '';\n  let lastName = '';\n  \n  if (localPart.includes('.')) {\n    const parts = localPart.split('.');\n    firstName = parts[0];\n    lastName = parts[parts.length - 1];\n  } else if (localPart.includes('_')) {\n    const parts = localPart.split('_');\n    firstName = parts[0];\n    lastName = parts[parts.length - 1];\n  } else {\n    firstName = localPart;\n  }\n  \n  // Capitalize names\n  firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);\n  lastName = lastName.charAt(0).toUpperCase() + lastName.slice(1);\n  \n  // Determine likely role\n  const roleIndicators = {\n    'ceo': ['ceo', 'founder', 'owner'],\n    'marketing': ['marketing', 'promo', 'advertis'],\n    'sales': ['sales', 'business', 'bd'],\n    'info': ['info', 'hello', 'contact', 'general'],\n    'support': ['support', 'help', 'service']\n  };\n  \n  let likelyRole = 'contact';\n  for (const [role, indicators] of Object.entries(roleIndicators)) {\n    if (indicators.some(indicator => localPart.includes(indicator))) {\n      likelyRole = role;\n      break;\n    }\n  }\n  \n  results.push({\n    ...input,\n    email: email,\n    first_name: firstName,\n    last_name: lastName,\n    full_name: `${firstName} ${lastName}`.trim(),\n    likely_role: likelyRole,\n    email_domain: domain,\n    priority_score: calculatePriorityScore(email, likelyRole, input),\n    personalization_data: {\n      business_name: input.business_name,\n      business_type: input.business_types?.[0] || 'business',\n      website: input.website,\n      rating: input.rating,\n      address: input.address,\n      product_relevance: calculateProductRelevance(input)\n    }\n  });\n}\n\nfunction calculatePriorityScore(email, role, businessData) {\n  let score = 0;\n  \n  // Role-based scoring\n  const roleScores = {\n    'ceo': 10,\n    'marketing': 8,\n    'sales': 7,\n    'info': 5,\n    'support': 3\n  };\n  score += roleScores[role] || 4;\n  \n  // Business quality scoring\n  score += Math.min(businessData.rating || 0, 5);\n  score += Math.min((businessData.review_count || 0) / 20, 5);\n  \n  return Math.round(score * 10) / 10;\n}\n\nfunction calculateProductRelevance(businessData) {\n  const businessTypes = (businessData.business_types || []).join(' ').toLowerCase();\n  const productDesc = (businessData.product_description || '').toLowerCase();\n  \n  // Simple keyword matching for relevance\n  const relevanceKeywords = {\n    'marketing': ['marketing', 'advertising', 'promotion', 'content'],\n    'sales': ['sales', 'crm', 'lead', 'customer'],\n    'tech': ['software', 'saas', 'digital', 'online', 'web'],\n    'ecommerce': ['store', 'shop', 'retail', 'ecommerce']\n  };\n  \n  let relevanceScore = 0;\n  for (const [category, keywords] of Object.entries(relevanceKeywords)) {\n    if (keywords.some(keyword => \n      businessTypes.includes(keyword) || productDesc.includes(keyword)\n    )) {\n      relevanceScore += 1;\n    }\n  }\n  \n  return Math.min(relevanceScore, 3);\n}\n\n// Sort by priority score\nresults.sort((a, b) => b.priority_score - a.priority_score);\n\nreturn results;"}, "id": "65564182-1d3f-4410-9e79-e9351a797714", "name": "Validate & Enrich Email Data", "type": "n8n-nodes-base.code", "position": [-1480, 40], "typeVersion": 2}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": {"fields": [{"fieldName": "email"}]}, "options": {}}, "id": "f7750398-6bc6-4d0d-9a6b-6a61d24a5e50", "name": "Final Email Deduplication", "type": "n8n-nodes-base.removeDuplicates", "position": [-1260, 40], "typeVersion": 1.1}, {"parameters": {"options": {}}, "id": "67b2cbe0-0f3c-4340-a70e-3709b732ee32", "name": "Loop Through Final Email List", "type": "n8n-nodes-base.splitInBatches", "position": [-1040, 40], "typeVersion": 3}, {"parameters": {}, "id": "3b95ad0d-f841-4ad9-acf2-091158bf1ae0", "name": "Generate Hyper-Personalized Email", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-820, 40], "typeVersion": 1.5}, {"parameters": {"jsCode": "const input = $input.first()?.json;\nlet emailData = {};\n\ntry {\n  // Try to parse the LLM response as JSON\n  const response = input.response || input.text || '';\n  \n  // Clean the response - remove markdown code blocks if present\n  const cleanResponse = response.replace(/```json\\n?/g, '').replace(/```\\n?/g, '');\n  \n  emailData = JSON.parse(cleanResponse);\n} catch (error) {\n  console.error('Failed to parse email JSON:', error);\n  \n  // Fallback parsing\n  const response = input.response || input.text || '';\n  emailData = {\n    subject: `Boost ${input.business_name}'s Growth with ${input.product_name}`,\n    body: `Hi ${input.first_name},\\n\\nI noticed ${input.business_name} has a great ${input.rating}-star rating. Impressive!\\n\\nI thought you might be interested in ${input.product_name} - ${input.product_description}\\n\\nWould you be open to a quick 15-minute conversation about how this could benefit your business?\\n\\nBest regards`,\n    cta: \"Reply if you'd like to learn more\"\n  };\n}\n\nreturn [{\n  json: {\n    ...input,\n    generated_subject: emailData.subject,\n    generated_body: emailData.body,\n    generated_cta: emailData.cta,\n    email_ready: true,\n    generation_timestamp: new Date().toISOString()\n  }\n}];"}, "id": "c1eded47-a22e-4d69-99c4-23ff9919a7e1", "name": "Parse Generated Email", "type": "n8n-nodes-base.code", "position": [-600, 40], "typeVersion": 2}, {"parameters": {"jsCode": "const input = $input.first()?.json;\nconst totalAccounts = 10; // Number of configured SMTP accounts\n\n// Use email hash for consistent but distributed selection\nconst emailHash = hashCode(input.email);\nconst accountIndex = Math.abs(emailHash) % totalAccounts;\n\n// Add some randomization to avoid patterns\nconst randomOffset = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1\nconst finalAccountIndex = Math.max(0, Math.min(totalAccounts - 1, accountIndex + randomOffset));\n\n// Calculate intelligent delay\nconst baseDelay = 45; // 45 seconds base\nconst randomDelay = Math.random() * 60; // 0-60 seconds random\nconst priorityMultiplier = Math.max(0.5, (10 - input.priority_score) / 10); // Higher priority = shorter delay\n\nconst totalDelay = Math.round((baseDelay + randomDelay) * priorityMultiplier);\n\nfunction hashCode(str) {\n  let hash = 0;\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash;\n}\n\nreturn [{\n  json: {\n    ...input,\n    smtp_account_index: finalAccountIndex,\n    delay_seconds: totalDelay,\n    send_timestamp: new Date().toISOString()\n  }\n}];"}, "id": "6fb73b06-3ba0-4749-889c-9b169f5955ff", "name": "Smart SMTP Account Selection", "type": "n8n-nodes-base.code", "position": [-380, 40], "typeVersion": 2}, {"parameters": {"rules": {"rules": [{"operation": "equal", "value2": "0"}, {"operation": "equal", "value2": "1"}, {"operation": "equal", "value2": "2"}, {"operation": "equal", "value2": "3"}, {"operation": "equal", "value2": "4"}, {"operation": "equal", "value2": "5"}, {"operation": "equal", "value2": "6"}, {"operation": "equal", "value2": "7"}, {"operation": "equal", "value2": "8"}, {"operation": "equal", "value2": "9"}]}}, "id": "ee544228-4a4f-454a-996e-3acb08e8adc4", "name": "SMTP Account Router", "type": "n8n-nodes-base.switch", "position": [-160, 40], "typeVersion": 2}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "d68b6bb2-5575-4630-bd55-bb0c689b583c", "name": "Gmail Account 1", "type": "n8n-nodes-base.gmail", "position": [60, -60], "typeVersion": 2.1, "webhookId": "eec69c53-fb6f-4a39-9a3b-fe23b0e41841"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "06875ace-68e7-4ccd-85f4-28ef73d57629", "name": "Gmail Account 2", "type": "n8n-nodes-base.gmail", "position": [60, 0], "typeVersion": 2.1, "webhookId": "2365567f-4d7a-4573-ac4d-674524c3e09f"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "320a607e-84b6-48fc-a733-9b3d1b149f40", "name": "Gmail Account 3", "type": "n8n-nodes-base.gmail", "position": [60, 60], "typeVersion": 2.1, "webhookId": "15a63180-f1e2-448c-a14e-23348792ef65"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "f0ac3215-2c1e-40aa-8dd6-8770954e151b", "name": "Gmail Account 4", "type": "n8n-nodes-base.gmail", "position": [60, 120], "typeVersion": 2.1, "webhookId": "0334aa0f-f73c-482c-b41f-1ddc843a1248"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "ab431601-5fb2-405b-b3cd-9e5c67311467", "name": "Gmail Account 5", "type": "n8n-nodes-base.gmail", "position": [60, 180], "typeVersion": 2.1, "webhookId": "cef10f13-e432-4eb5-bbc7-a5ac66913879"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "8f1c8e12-da6c-42ed-9a0d-869d62bf37c5", "name": "<PERSON>mail Account 6", "type": "n8n-nodes-base.gmail", "position": [60, 240], "typeVersion": 2.1, "webhookId": "083db020-9853-4133-83e0-cd03a48a8ac1"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "6708cbae-13aa-4979-b389-c1831a730332", "name": "Gmail Account 7", "type": "n8n-nodes-base.gmail", "position": [60, 300], "typeVersion": 2.1, "webhookId": "c2afd19a-1bf2-4bf2-ac8c-b9e944f6e201"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "7344e5b7-6c8d-4e50-a6ea-878460574b01", "name": "Gmail Account 8", "type": "n8n-nodes-base.gmail", "position": [60, 360], "typeVersion": 2.1, "webhookId": "1d9158cf-9e74-4beb-b962-336f81110772"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "3187c230-3500-44f6-a409-08fb7231930d", "name": "Gmail Account 9", "type": "n8n-nodes-base.gmail", "position": [60, 420], "typeVersion": 2.1, "webhookId": "d25b5324-6986-4be2-ab56-0d1cd9f40b9a"}, {"parameters": {"subject": "={{ $json.generated_subject }}", "message": "={{ $json.generated_body }}", "options": {"bccList": "", "ccList": "", "replyTo": ""}}, "id": "c496db51-7b9b-418e-b394-90b5ec00a139", "name": "Gmail Account 10", "type": "n8n-nodes-base.gmail", "position": [60, 480], "typeVersion": 2.1, "webhookId": "993b7db4-ef9b-44cd-99a9-f6262bd6e54a"}, {"parameters": {"jsCode": "const input = $input.first()?.json;\n\n// Log successful email send\nconsole.log(`✅ Email sent successfully to: ${input.email}`);\nconsole.log(`📧 Subject: ${input.generated_subject}`);\nconsole.log(`🏢 Business: ${input.business_name}`);\nconsole.log(`📊 Priority Score: ${input.priority_score}`);\nconsole.log(`🔄 SMTP Account: ${input.smtp_account_index}`);\nconsole.log(`⏰ Timestamp: ${new Date().toISOString()}`);\n\nreturn [{\n  json: {\n    ...input,\n    email_sent: true,\n    sent_timestamp: new Date().toISOString(),\n    status: 'success'\n  }\n}];"}, "id": "8ab1a611-c864-4d38-854c-033f6c3dd171", "name": "Log Email Success", "type": "n8n-nodes-base.code", "position": [280, 200], "typeVersion": 2}, {"parameters": {"amount": "={{ $json.delay_seconds }}"}, "id": "9c5a5728-c8fb-4320-918c-d8d4a4f3e5fc", "name": "Smart Delay Before Next Email", "type": "n8n-nodes-base.wait", "position": [500, 200], "typeVersion": 1.1, "webhookId": "c4e34d0c-bdff-46fa-9b58-1917c0be37af"}, {"parameters": {"jsCode": "const input = $input.first()?.json;\n\n// Track campaign analytics\nconst analytics = {\n  campaign_id: `${input.product_name.replace(/\\s+/g, '_').toLowerCase()}_${new Date().toISOString().split('T')[0]}`,\n  email_sent: input.email,\n  business_name: input.business_name,\n  product_promoted: input.product_name,\n  priority_score: input.priority_score,\n  smtp_account_used: input.smtp_account_index,\n  subject_line: input.generated_subject,\n  send_timestamp: input.sent_timestamp,\n  business_rating: input.rating,\n  business_type: input.personalization_data?.business_type,\n  estimated_open_probability: calculateOpenProbability(input),\n  personalization_level: calculatePersonalizationLevel(input)\n};\n\nfunction calculateOpenProbability(data) {\n  let probability = 0.15; // Base 15% open rate\n  \n  // Adjust based on priority score\n  probability += (data.priority_score / 20);\n  \n  // Adjust based on business rating\n  if (data.rating >= 4.5) probability += 0.05;\n  if (data.rating >= 4.0) probability += 0.03;\n  \n  // Adjust based on personalization\n  if (data.generated_subject.includes(data.business_name)) probability += 0.08;\n  if (data.generated_body.includes(data.business_name)) probability += 0.05;\n  \n  return Math.min(0.45, Math.round(probability * 100) / 100);\n}\n\nfunction calculatePersonalizationLevel(data) {\n  let level = 0;\n  \n  if (data.generated_subject.includes(data.business_name)) level += 2;\n  if (data.generated_body.includes(data.business_name)) level += 2;\n  if (data.generated_body.includes(data.rating?.toString())) level += 1;\n  if (data.generated_body.includes(data.address)) level += 1;\n  if (data.likely_role !== 'contact') level += 1;\n  \n  return Math.min(10, level);\n}\n\nconsole.log('📊 Campaign Analytics:', JSON.stringify(analytics, null, 2));\n\nreturn [{\n  json: {\n    ...input,\n    analytics,\n    workflow_completed: true\n  }\n}];"}, "id": "b9cc1c9d-650e-45eb-9076-f3c7a3940b58", "name": "Analytics & Tracking", "type": "n8n-nodes-base.code", "position": [720, 200], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "https://your-analytics-endpoint.com/email-campaign-completion", "options": {"timeout": 5000}}, "id": "c85f3c54-dc1d-4609-a003-2fcc4c3cb0bf", "name": "Webhook: Campaign Completion", "type": "n8n-nodes-base.httpRequest", "position": [940, 200], "typeVersion": 4.2, "onError": "continueRegularOutput"}], "pinData": {"MANUAL TRIGGER: Start Enhanced Workflow": [{"json": {"product_link": "https://www.example.com/ai-content-pro", "product_name": "AI Content Generator Pro", "product_description": "A SaaS tool that automatically generates high-quality, SEO-optimized blog posts and marketing copy using advanced AI, saving businesses hours of work and boosting their online visibility."}}, {"json": {"product_link": "https://www.example.com/sales-automator", "product_name": "Sales Outreach Automator", "product_description": "An AI-driven solution to automate cold email outreach and follow-ups with personalization and scheduling."}}, {"json": {"product_link": "https://www.example.com/seo-boost-toolkit", "product_name": "SEO Boost Toolkit", "product_description": "A suite that analyzes websites and automatically applies SEO best practices to improve rankings."}}, {"json": {"product_link": "https://www.example.com/social-scheduler", "product_name": "Social Media Scheduler", "product_description": "An AI platform to plan, auto-post, and optimize social media content for maximum engagement."}}, {"json": {"product_link": "https://www.example.com/customer-insights", "product_name": "Customer Insights Dashboard", "product_description": "A real-time analytics dashboard powered by AI that surfaces key customer behavior insights."}}]}, "connections": {"MANUAL TRIGGER: Start Enhanced Workflow": {"main": [[{"node": "Configuration & Settings", "type": "main", "index": 0}]]}, "Configuration & Settings": {"main": [[{"node": "Loop Through Products", "type": "main", "index": 0}]]}, "Loop Through Products": {"main": [[{"node": "Loop Through Products", "type": "main", "index": 0}], [{"node": "Generate Enhanced Search Queries (GPT-4)", "type": "main", "index": 0}]]}, "Generate Enhanced Search Queries (GPT-4)": {"main": [[{"node": "Parse Generated Queries", "type": "main", "index": 0}]]}, "Parse Generated Queries": {"main": [[{"node": "Loop Through Search Queries", "type": "main", "index": 0}]]}, "Loop Through Search Queries": {"main": [[{"node": "Loop Through Products", "type": "main", "index": 0}], [{"node": "Intelligent Rate Limiting", "type": "main", "index": 0}]]}, "Intelligent Rate Limiting": {"main": [[{"node": "Dynamic Wait", "type": "main", "index": 0}]]}, "Dynamic Wait": {"main": [[{"node": "Enhanced Google Maps API Search", "type": "main", "index": 0}]]}, "Enhanced Google Maps API Search": {"main": [[{"node": "Extract & Enrich Business Data", "type": "main", "index": 0}]]}, "Loop Through Businesses": {"main": [[{"node": "Loop Through Search Queries", "type": "main", "index": 0}], [{"node": "Advanced Website Scraping", "type": "main", "index": 0}]]}, "Advanced Website Scraping": {"main": [[{"node": "Try Scraping Method", "type": "main", "index": 0}]]}, "Try Scraping Method": {"main": [[{"node": "Process Scraped Content & Extract Emails", "type": "main", "index": 0}]]}, "Process Scraped Content & Extract Emails": {"main": [[{"node": "Validate & Enrich Email Data", "type": "main", "index": 0}]]}, "Validate & Enrich Email Data": {"main": [[{"node": "Final Email Deduplication", "type": "main", "index": 0}]]}, "Final Email Deduplication": {"main": [[{"node": "Loop Through Final Email List", "type": "main", "index": 0}]]}, "Loop Through Final Email List": {"main": [[{"node": "Loop Through Businesses", "type": "main", "index": 0}], [{"node": "Generate Hyper-Personalized Email", "type": "main", "index": 0}]]}, "Generate Hyper-Personalized Email": {"main": [[{"node": "Parse Generated Email", "type": "main", "index": 0}]]}, "Parse Generated Email": {"main": [[{"node": "Smart SMTP Account Selection", "type": "main", "index": 0}]]}, "Smart SMTP Account Selection": {"main": [[{"node": "SMTP Account Router", "type": "main", "index": 0}]]}, "SMTP Account Router": {"main": [[{"node": "Gmail Account 1", "type": "main", "index": 0}], [{"node": "Gmail Account 2", "type": "main", "index": 0}], [{"node": "Gmail Account 3", "type": "main", "index": 0}], [{"node": "Gmail Account 4", "type": "main", "index": 0}], [{"node": "Gmail Account 5", "type": "main", "index": 0}], [{"node": "<PERSON>mail Account 6", "type": "main", "index": 0}], [{"node": "Gmail Account 7", "type": "main", "index": 0}], [{"node": "Gmail Account 8", "type": "main", "index": 0}], [{"node": "Gmail Account 9", "type": "main", "index": 0}], [{"node": "Gmail Account 10", "type": "main", "index": 0}]]}, "Gmail Account 1": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 2": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 3": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 4": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 5": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 6": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 7": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 8": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 9": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Gmail Account 10": {"main": [[{"node": "Log Email Success", "type": "main", "index": 0}]]}, "Log Email Success": {"main": [[{"node": "Smart Delay Before Next Email", "type": "main", "index": 0}]]}, "Smart Delay Before Next Email": {"main": [[{"node": "Analytics & Tracking", "type": "main", "index": 0}]]}, "Analytics & Tracking": {"main": [[{"node": "Webhook: Campaign Completion", "type": "main", "index": 0}]]}, "Webhook: Campaign Completion": {"main": [[{"node": "Loop Through Final Email List", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8050a6d0-34f7-49d8-ade8-7af8e55dfc67", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05d72590728e7f00a26a18e0a23c28395284277b7fecb6237a670b849d013644"}, "id": "34OqVCA1Vcw4Wtp5", "tags": []}